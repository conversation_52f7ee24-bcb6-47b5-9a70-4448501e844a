import * as Yup from 'yup';

export interface IForgotConfig {
    // Add any forgot password configuration fields here
    email?: string;
    resetToken?: string;
}

export const defaultForgotConfig: IForgotConfig = {
    email: '',
    resetToken: ''
};

export interface ILoginConfig {
    username: string;
    password: string;
    rememberMe: boolean;
}

export const loginConfig: ILoginConfig = {
    username: '',
    password: '',
    rememberMe: false
};

export const loginSchema = Yup.object().shape({
    username: Yup.string().required('Username is required').min(3, 'Username must be at least 3 characters'),
    password: Yup.string().required('Password is required').min(6, 'Password must be at least 6 characters'),
    rememberMe: Yup.boolean()
});

export interface IRegisterConfig {
    firstName: string;
    lastName: string;
    email: string;
    username: string;
    password: string;
    confirmPassword: string;
    companyName: string;
    phone: string;
    agreeToTerms: boolean;
}

export const registerConfig: IRegisterConfig = {
    firstName: '',
    lastName: '',
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
    companyName: '',
    phone: '',
    agreeToTerms: false
};

export const registerSchema = Yup.object().shape({
    firstName: Yup.string().required('First name is required').min(2, 'First name must be at least 2 characters'),
    lastName: Yup.string().required('Last name is required').min(2, 'Last name must be at least 2 characters'),
    email: Yup.string().email('Invalid email format').required('Email is required'),
    username: Yup.string().required('Username is required').min(3, 'Username must be at least 3 characters'),
    password: Yup.string().required('Password is required').min(6, 'Password must be at least 6 characters'),
    confirmPassword: Yup.string()
        .required('Confirm password is required')
        .oneOf([Yup.ref('password')], 'Passwords must match'),
    companyName: Yup.string().required('Company name is required'),
    phone: Yup.string().required('Phone number is required'),
    agreeToTerms: Yup.boolean().oneOf([true], 'You must agree to the terms and conditions')
});

import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { Provider } from 'react-redux';
import CryptoJS from 'crypto-js';

import { ConfigProvider } from 'contexts/ConfigContext';
import { injectStore } from 'services/ApiService';
import { BASE_PATH } from 'constants/Config';
import { store } from './app/store';
import App from 'App';

// style + assets
import 'assets/scss/style.scss';

// ==============================|| REACT DOM RENDER  ||============================== //

// Environment variables from Vite
export const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
export const reportUrl = import.meta.env.VITE_REPORT_SERVICE_URL_API || 'http://localhost:3000/api/v1/report';
export const authUrl = import.meta.env.VITE_AUTH_SERVICE_URL_API || 'http://localhost:3000/api/v1/auth';
export const syncUrl = import.meta.env.VITE_BASE_URL_SYNC || 'http://localhost:3000/api/v1/sync/report';
export const privateKeyHasLogin = CryptoJS.enc.Utf8.parse(import.meta.env.VITE_PRIVATE_KEY_HASH_LOGIN || '5hr5y4OBD3EKnN19');
export const cryptoJSConfigMode = {
    iv: CryptoJS.enc.Utf8.parse(import.meta.env.VITE_INITIALIZATION_VECTOR || 'r9QTBzqE1983QJnM'),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
};

const container = document.getElementById('root');
const root = createRoot(container!);

// Initialize store and render app
injectStore(store);

root.render(
    <Provider store={store}>
        <ConfigProvider>
            <BrowserRouter basename={BASE_PATH}>
                <App />
            </BrowserRouter>
        </ConfigProvider>
    </Provider>
);

import { Box, Button, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';

import { closeDeniedPermission } from 'store/slice/deniedPermissionSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { PUBLIC_URL } from 'constants/Common';
import { ROUTER } from 'constants/Routers';
// import { Crown } from './icons';

interface Props {
    children?: JSX.Element;
}

const DeniedPermissionScreen = ({ children }: Props) => {
    const { show, isTabWrap } = useAppSelector((state) => state.deniedPermission);

    const dispatch = useAppDispatch();

    const navigate = useNavigate();

    const handleClose = () => {
        navigate(ROUTER.home.index);
        dispatch(closeDeniedPermission());
    };

    return children && !show && !isTabWrap ? (
        children
    ) : (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: '100%',
                height: '100%',
                background: `url("${PUBLIC_URL}background-dashboard.svg") no-repeat center`
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '50%',
                    flexDirection: 'column',
                    gap: 5,
                    py: 10
                }}
            >
                <Box
                    sx={{
                        padding: 2,
                        border: '2px solid #f99e2185',
                        borderRadius: 30
                    }}
                >
                    {/* <Crown /> */}
                </Box>
                <Typography
                    variant="h1"
                    sx={{
                        fontSize: 24,
                        textAlign: 'center'
                    }}
                >
                    Upgrade to Access the full feature of InstantView
                </Typography>
                <Typography variant="body2" sx={{ fontSize: 16, textAlign: 'center' }}>
                    Unlock limitless content possibilities. Upgrade now to exceed your credit limit and access valuable content that fuels
                    creativity. Get more credit and unleash your full potential with our paid plans.
                </Typography>

                <Button size="large" variant="contained" onClick={handleClose}>
                    <FormattedMessage id="upgrade-btn" />
                </Button>
            </Box>
        </Box>
    );
};

export default DeniedPermissionScreen;

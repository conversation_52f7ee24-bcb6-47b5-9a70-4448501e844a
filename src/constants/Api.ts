const METHOD = {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE',
    PATCH: 'PATCH'
};

const Api = {
    auth: {
        login: (isLdap: boolean = false) => ({
            method: METHOD.POST,
            url: `/api/auth/login?ldap=${isLdap}`,
            dontNeedToken: true
        }),
        refreshToken: (isLdap: string = 'false') => ({
            method: METHOD.POST,
            url: `/api/auth/refreshToken?ldap=${isLdap}`,
            dontNeedToken: true
        }),
        me: {
            method: METHOD.GET,
            url: '/api/auth/me',
            dontNeedToken: false
        },
        changePassword: (userId: string) => ({
            method: METHOD.PUT,
            url: `/api/auth/changePassword/${userId}`,
            dontNeedToken: false
        }),
        register: {
            method: METHOD.POST,
            url: '/api/auth/register',
            dontNeedToken: true
        },
        forgot: (email: string) => ({
            method: METHOD.POST,
            url: `/api/auth/forgotPassword?email=${email}`,
            dontNeedToken: true
        }),
        createPassword: {
            method: METHOD.POST,
            url: '/api/auth/createPassword',
            dontNeedToken: true
        },
        verifyToken: {
            method: METHOD.POST,
            url: '/api/auth/verifyToken',
            dontNeedToken: true
        },
        getAllpackageAccount: {
            method: METHOD.GET,
            url: '/api/auth/package-account/findAll',
            dontNeedToken: true
        }
    },
    master: {
        getAllTitle: {
            method: METHOD.GET,
            url: '/api/master/titleAll',
            dontNeedToken: false
        },
        getAllRank: {
            method: METHOD.GET,
            url: '/api/master/rankAll',
            dontNeedToken: false
        },
        getAllGroup: {
            method: METHOD.GET,
            url: '/api/master/groupAll',
            dontNeedToken: false
        },
        getProjectType: (fixCost?: boolean) => ({
            method: METHOD.GET,
            url: '/api/master/projectTypeAll' + (fixCost === undefined ? '' : `?fixCost=${fixCost}`),
            dontNeedToken: false
        }),
        getProjectTypeNonBillableConfig: (nonBillable?: boolean) => ({
            method: METHOD.GET,
            url: '/api/project/type/findByBillAbleType' + (nonBillable === undefined ? '' : `?billable=nonbillable`),
            dontNeedToken: false
        }),
        getProjectAll: (fixCost?: boolean) => ({
            method: METHOD.GET,
            url: `/api/master/projectAll?fixCost=${fixCost || false}`,
            dontNeedToken: false
        }),
        getProjectAllWithoutFixcost: {
            method: METHOD.GET,
            url: '/api/master/projectAll',
            dontNeedToken: false
        },
        findAllUserLoginTime: {
            method: METHOD.GET,
            url: '/api/master/findUserLogtime',
            dontNeedToken: false
        },
        getFunctionAll: {
            method: METHOD.GET,
            url: '/api/master/functionAll',
            dontNeedToken: false
        },
        getProductionPerformanceAll: {
            method: METHOD.GET,
            url: '/api/master/productionPerformanceAllVND',
            dontNeedToken: false
        },
        getAllLanguage: {
            method: METHOD.GET,
            url: '/api/master/languageAll',
            dontNeedToken: false
        }
    },
    synchronize: {
        getDownloadTemplate: {
            method: METHOD.GET,
            url: 'master/excel/download',
            dontNeedToken: false
        },
        postImportExcel: {
            method: METHOD.POST,
            url: 'sync/manual/import/excel',
            dontNeedToken: false
        }
    },
    weekly_efford: {
        getMember: {
            method: METHOD.GET,
            url: 'week/member',
            dontNeedToken: false
        },
        getProject: {
            method: METHOD.GET,
            url: 'week/project',
            dontNeedToken: false
        },
        getProjectDetail: {
            method: METHOD.GET,
            url: 'week/projectDetail',
            dontNeedToken: false
        },
        postVerified: {
            method: METHOD.POST,
            url: 'week/projectDetailVerified',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'week/download',
            dontNeedToken: false
        },
        getWeeklyEffortProject: {
            method: METHOD.GET,
            url: 'week/getAllProject',
            dontNeedToken: false
        }
    },
    monthly_efford: {
        postUpdateEffordPlan: {
            method: METHOD.POST,
            url: 'month/saveOrUpdateEffortPlan',
            dontNeedToken: false
        },
        getSummary: {
            method: METHOD.GET,
            url: 'month/summary',
            dontNeedToken: false
        },
        getProject: {
            method: METHOD.GET,
            url: 'month/project',
            dontNeedToken: false
        },
        getMember: {
            method: METHOD.GET,
            url: 'month/member',
            dontNeedToken: false
        },
        getMemberProject: {
            method: METHOD.GET,
            url: 'month/memberProject',
            dontNeedToken: false
        },

        getDownload: {
            method: METHOD.GET,
            url: 'month/download',
            dontNeedToken: false
        },

        // mr/
        getProjectReports: {
            method: METHOD.GET,
            url: 'project/findAllReportProjects',
            dontNeedToken: false
        },
        addProjectReports: {
            method: METHOD.POST,
            url: 'project/create',
            dontNeedToken: false
        },
        editProjectReports: (id: string) => ({
            method: METHOD.PUT,
            url: `project/${id}`,
            dontNeedToken: false
        }),
        getReportById: (id: string) => ({
            method: METHOD.GET,
            url: `project/reportProject/${id}`,
            dontNeedToken: false
        }),

        geProjectDetailById: {
            method: METHOD.GET,
            url: `project/getProjectWithTotalHC`,
            dontNeedToken: false
        },
        // project reports
        getDownloadReport: {
            method: METHOD.GET,
            url: 'project/downloadReport',
            dontNeedToken: false
        },
        deleteReport: (id: string) => ({
            method: METHOD.DELETE,
            url: `project/${id}`,
            dontNeedToken: false
        }),
        getOMReport: {
            method: METHOD.GET,
            url: 'orm/getAll',
            dontNeedToken: false
        },
        uploadOrmReport: {
            method: METHOD.POST,
            url: 'orm/uploadDocument',
            dontNeedToken: false
        },
        deleteOrmReport: (id: string) => ({
            method: METHOD.DELETE,
            url: `orm/${id}`,
            dontNeedToken: false
        }),
        downloadOrmReport: (id: string) => ({
            method: METHOD.GET,
            url: `orm/downloadDocument/${id}`,
            dontNeedToken: false
        })
    },
    list_project_team: {
        getListProjectTeam: {
            method: METHOD.GET,
            url: 'project/team',
            dontNeedToken: false
        },
        getRatioJoiningProject: {
            method: METHOD.GET,
            url: 'project/ratio/join',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'project/download',
            dontNeedToken: false
        }
    },
    non_billable_monitoring: {
        getAll: {
            method: METHOD.GET,
            url: 'nonbill/nonBillAble',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'nonbill/download',
            dontNeedToken: false
        },
        getCostByWeek: {
            method: METHOD.GET,
            url: 'nonbill/costByWeek',
            dontNeedToken: false
        },
        getWarningNonbillable: {
            method: METHOD.GET,
            url: 'nonbill/warningNonbillable',
            dontNeedToken: false
        },
        findAllConfig: {
            method: METHOD.GET,
            url: 'nonBillableConfig/findAllConfig',
            dontNeedToken: false
        }
    },
    monthly_project_cost: {
        getSummary: (fixCost?: boolean) => ({
            method: METHOD.GET,
            url: `monthlyProjectCost/summary?fixCost=${fixCost || false}`,
            dontNeedToken: false
        }),
        getDownload: {
            method: METHOD.GET,
            url: 'monthlyProjectCost/download',
            dontNeedToken: false
        },
        getDetailReportByMonth: {
            method: METHOD.GET,
            url: 'monthlyProjectCost/detailMonth',
            dontNeedToken: false
        },
        getMonthlyCost: {
            method: METHOD.GET,
            url: 'monthlyProjectCost/monthlyCost',
            dontNeedToken: false
        },
        getFindProjectCost: {
            method: METHOD.GET,
            url: 'monthlyProjectCost/findProjectCost',
            dontNeedToken: false
        },
        getDownloadTemplateMonthlyCost: {
            method: METHOD.GET,
            url: 'monthlyProjectCost/downloadTemplate',
            dontNeedToken: false
        },
        postImportTemplateMonthlyCost: {
            method: METHOD.POST,
            url: 'monthlyProjectCost/importTemplate',
            upload: true,
            dontNeedToken: false
        },
        postSaveOrUpdateActualCost: {
            method: METHOD.POST,
            url: 'monthlyProjectCost/saveOrUpdateActualCost',
            dontNeedToken: false
        },
        deleteActualCost: {
            method: METHOD.DELETE,
            url: 'monthlyProjectCost/delete',
            dontNeedToken: false
        }
    },
    cost_monitoring: {
        getAll: {
            method: METHOD.GET,
            url: 'month/costMonitoring/index',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'month/costMonitoring/download',
            dontNeedToken: false
        },
        getEffortByWeek: {
            method: METHOD.GET,
            url: 'month/costMonitoring/effortByWeek',
            dontNeedToken: false
        }
    },
    member: {
        getAll: {
            method: METHOD.GET,
            url: 'user/findAllUser',
            dontNeedToken: false
        },
        postSaveOrUpdate: {
            method: METHOD.POST,
            url: 'user/saveOrUpdateUser',
            dontNeedToken: false
        },
        resetPassowrd: (userId: string) => ({
            method: METHOD.PUT,
            url: `user/resetPassword/${userId}`,
            dontNeedToken: false
        })
    },
    department: {
        getAll: {
            method: METHOD.GET,
            url: 'getAllDepartment',
            dontNeedToken: false
        },
        search: {
            method: METHOD.GET,
            url: 'search',
            dontNeedToken: false
        },
        create: {
            method: METHOD.POST,
            url: 'create',
            dontNeedToken: false
        },
        edit: (departmentId: string) => ({
            method: METHOD.PUT,
            url: `${departmentId}`,
            dontNeedToken: false
        }),
        delete: (departmentId: string) => ({
            method: METHOD.DELETE,
            url: `${departmentId}`,
            dontNeedToken: false
        })
    },
    project: {
        getAll: {
            method: METHOD.GET,
            url: 'project/getAllProject',
            dontNeedToken: false
        },
        getDetail: {
            method: METHOD.GET,
            url: 'project/getInfoProjectDetail',
            dontNeedToken: false
        },
        getQuotaUpdateHistory: {
            method: METHOD.GET,
            url: 'project/getInfoProjectQuotaHistory',
            dontNeedToken: false
        },
        saveOrUpdate: {
            method: METHOD.POST,
            url: 'project/saveOrUpdate',
            dontNeedToken: false
        },
        saveOrUpdateProjectUser: {
            method: METHOD.POST,
            url: 'project/saveOrUpdateProjectUser',
            dontNeedToken: false
        },
        deleteProjectUser: {
            method: METHOD.DELETE,
            url: 'project/deleteProjectUser',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'project/export',
            dontNeedToken: false
        },
        typeConfig: {
            search: {
                method: METHOD.GET,
                url: 'project/type/search',
                dontNeedToken: false
            },
            create: {
                method: METHOD.POST,
                url: 'project/type/create',
                dontNeedToken: false
            },
            edit: (projectTypeId: string) => ({
                method: METHOD.PUT,
                url: `project/type/${projectTypeId}`,
                dontNeedToken: false
            }),
            delete: (projectTypeId: string) => ({
                method: METHOD.DELETE,
                url: `project/type/${projectTypeId}`,
                dontNeedToken: false
            })
        }
    },
    nonBillableConfig: {
        search: {
            method: METHOD.GET,
            url: 'nonBillableConfig/findAll',
            dontNeedToken: false
        },
        edit: (configId: string) => ({
            method: METHOD.PUT,
            url: `nonBillableConfig/update/${configId}`,
            dontNeedToken: false
        })
    },
    title: {
        search: {
            method: METHOD.GET,
            url: 'title/search',
            dontNeedToken: false
        },
        create: {
            method: METHOD.POST,
            url: 'title/create',
            dontNeedToken: false
        },
        edit: (titleId: string) => ({
            method: METHOD.PUT,
            url: `title/${titleId}`,
            dontNeedToken: false
        }),
        delete: (titleId: string) => ({
            method: METHOD.DELETE,
            url: `title/${titleId}`,
            dontNeedToken: false
        })
    },
    special_hours: {
        getAll: {
            method: METHOD.GET,
            url: 'special/getAllSpecialHour',
            dontNeedToken: false
        },
        postSaveOrUpdateSpecialHours: {
            method: METHOD.POST,
            url: 'special/saveOrUpdate',
            dontNeedToken: false
        }
    },
    system_config: {
        getAll: {
            method: METHOD.GET,
            url: 'master/systemConfigAll',
            dontNeedToken: false
        },
        postUpdateConfig: {
            method: METHOD.POST,
            url: 'systemConfig/saveOrUpdate',
            dontNeedToken: false
        }
    },
    email_config: {
        getAll: {
            method: METHOD.GET,
            url: 'master/emailConfigAll',
            dontNeedToken: false
        },
        postSaveOrUpdateEmailConfig: {
            method: METHOD.POST,
            url: 'email/saveOrUpdate',
            dontNeedToken: false
        }
    },
    cv_config_technology: {
        getAll: {
            method: METHOD.GET,
            url: 'tech/getAll',
            dontNeedToken: false
        },
        postSaveOrUpdateCVConfig: {
            method: METHOD.POST,
            url: 'tech/createOrUpdate',
            dontNeedToken: false
        },
        deleteTechnology: {
            method: METHOD.DELETE,
            url: 'tech/delete',
            dontNeedToken: false
        }
    },
    cv_config_language: {
        getAll: {
            method: METHOD.GET,
            url: 'language/getAll',
            dontNeedToken: false
        },
        postSaveOrUpdateCVConfig: {
            method: METHOD.POST,
            url: 'language/saveOrUpdate',
            dontNeedToken: false
        },
        deleteLanguage: {
            method: METHOD.DELETE,
            url: 'language/delete',
            dontNeedToken: false
        }
    },
    cv_config_reference: {
        getAll: {
            method: METHOD.GET,
            url: 'references/getAll',
            dontNeedToken: false
        },
        postSaveOrUpdateCVConfig: {
            method: METHOD.POST,
            url: 'references/saveOrUpdate',
            dontNeedToken: false
        },
        deleteReference: {
            method: METHOD.DELETE,
            url: 'references/delete',
            dontNeedToken: false
        }
    },
    exchange_rate_config: {
        getAll: {
            method: METHOD.GET,
            url: 'exchange-rate',
            dontNeedToken: false
        },
        postSaveOrUpdateExchangeRateConfig: {
            method: METHOD.POST,
            url: 'exchange-rate/saveOrUpdate',
            dontNeedToken: false
        },
        deleteExchangeRate: {
            method: METHOD.DELETE,
            url: 'exchange-rate/delete',
            dontNeedToken: false
        }
    },
    holiday: {
        getAll: {
            method: METHOD.GET,
            url: 'holiday/getAll',
            dontNeedToken: false
        },
        postSaveOrUpdateHoliday: {
            method: METHOD.POST,
            url: 'holiday/saveOrUpdate',
            dontNeedToken: false
        },
        delete: {
            method: METHOD.DELETE,
            url: 'holiday/delete',
            dontNeedToken: false
        }
    },
    rank: {
        getAll: {
            method: METHOD.GET,
            url: 'rank/rankAll',
            dontNeedToken: false
        },
        postUpdateRank: {
            method: METHOD.POST,
            url: 'rank/createOrUpdateRank',
            dontNeedToken: false
        }
    },
    group: {
        getAll: {
            method: METHOD.GET,
            url: 'group/getGroupAll',
            dontNeedToken: false
        },
        postSaveOrUpdate: {
            method: METHOD.POST,
            url: 'group/saveOrUpdate',
            dontNeedToken: false
        }
    },
    sale_productivity: {
        getAll: {
            method: METHOD.GET,
            url: 'sales/productivityProject',
            dontNeedToken: false
        },
        getDetail: {
            method: METHOD.GET,
            url: 'sales/productivityProject/getDetail',
            dontNeedToken: false
        },
        postCreateOrUpdate: {
            method: METHOD.POST,
            url: 'sales/productivityProject/createOrUpdate',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'sales/productivityProject/download',
            dontNeedToken: false
        },
        postUpdateHeadCount: {
            method: METHOD.POST,
            url: 'sales/productivityProject/updateHeadcount',
            dontNeedToken: false
        },
        getDetailHeadCountByMonth: {
            method: METHOD.GET,
            url: 'sales/productivityProject/findHeadcount',
            dontNeedToken: false
        },
        postComment: {
            method: METHOD.POST,
            url: 'sales/productivityProject/comment',
            dontNeedToken: false
        },
        getStandardWorkingDay: {
            method: METHOD.GET,
            url: 'sales/productivityProject/standardWorkingDay',
            dontNeedToken: false
        },
        getExchangeRate: {
            method: METHOD.GET,
            url: 'sales/productivityProject/exchangeRate',
            dontNeedToken: false
        }
    },
    comment: {
        getFindComment: {
            method: METHOD.GET,
            url: 'comment/findComment',
            dontNeedToken: false
        },
        postSaveOrUpdateComment: {
            method: METHOD.POST,
            url: 'comment/saveOrUpdate',
            dontNeedToken: false
        },
        getFindCommentDetail: {
            method: METHOD.GET,
            url: 'commentDetail/findComment',
            dontNeedToken: false
        },
        postSaveOrUpdateCommentDetail: {
            method: METHOD.POST,
            url: 'commentDetail/saveOrUpdate',
            dontNeedToken: false
        }
    },
    sale_list: {
        getRequestAll: {
            method: METHOD.GET,
            url: 'sales/list/requests/getAll',
            dontNeedToken: false
        },
        getSupplierAll: {
            method: METHOD.GET,
            url: 'sales/list/supplier/getAll',
            dontNeedToken: false
        },
        postRequestsCreateOrUpdate: {
            method: METHOD.POST,
            url: 'sales/list/requests/createOrUpdate',
            dontNeedToken: false
        },
        deleteRequests: (id: string) => ({
            method: METHOD.DELETE,
            url: `sales/list/requests/delete?id=${id}`,
            dontNeedToken: false
        }),
        getDownload: {
            method: METHOD.GET,
            url: 'sales/list/download',
            dontNeedToken: false
        },
        postSupplierCreateOrUpdate: {
            method: METHOD.POST,
            url: 'sales/list/supplier/createOrUpdate',
            dontNeedToken: false
        },
        deleteSupplier: (id: string) => ({
            method: METHOD.DELETE,
            url: `sales/list/supplier/delete?id=${id}`,
            dontNeedToken: false
        }),
        getSaleSummary: {
            method: METHOD.GET,
            url: 'sales/summary',
            dontNeedToken: false
        },
        getDownloadSalePineLine: {
            method: METHOD.GET,
            url: 'sales/download',
            dontNeedToken: false
        },
        getTotalSummary: {
            method: METHOD.GET,
            url: 'sales/summary-total',
            dontNeedToken: false
        }
    },
    working_calendar: {
        getAll: {
            method: METHOD.GET,
            url: 'calendar/filterWorkingCalender',
            dontNeedToken: false
        },
        getType: {
            method: METHOD.GET,
            url: 'calendar/getTypeWorkingCalendar',
            dontNeedToken: false
        },
        postSaveOrUpdate: {
            method: METHOD.POST,
            url: 'calendar/saveOrUpdate',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'calendar/download',
            dontNeedToken: false
        },
        postUpdateStatus: {
            method: METHOD.POST,
            url: 'calendar/updateStatus',
            dontNeedToken: false
        },
        getClosingDate: {
            method: METHOD.GET,
            url: 'calendar/closing-dates',
            dontNeedToken: false
        },
        postUpdateClosingDate: {
            method: METHOD.POST,
            url: 'calendar/closing-date/update',
            dontNeedToken: false
        },
        postVerifyClosingDate: {
            method: METHOD.POST,
            url: 'calendar/closing-date/verify',
            dontNeedToken: false
        },
        findOnsite: {
            method: METHOD.GET,
            url: 'calendar/onsite',
            dontNeedToken: false
        },
        updateComment: {
            method: METHOD.PUT,
            url: 'calendar/updateComment',
            dontNeedToken: false
        }
    },
    manage_leaves: {
        getAll: {
            method: METHOD.GET,
            url: 'leaves',
            dontNeedToken: false
        },
        getLeaveDetail: (idHexString: string) => ({
            method: METHOD.GET,
            url: `leaves/detail/${idHexString}`,
            dontNeedToken: false
        }),
        getDetailUser: {
            method: METHOD.GET,
            url: 'leaves/detail-user',
            dontNeedToken: false
        },
        getDetailUserId: (idHexString: string) => {
            return { method: METHOD.GET, url: `leaves/detail/${idHexString}`, dontNeedToken: false };
        },
        putApproved: (idHexString: string) => {
            return {
                method: METHOD.PUT,
                url: `leaves/approved/${idHexString}`,
                dontNeedToken: false
            };
        },
        putReject: {
            method: METHOD.PUT,
            url: `leaves/reject`,
            dontNeedToken: false
        },
        postCreate: {
            method: METHOD.POST,
            url: 'leaves',
            dontNeedToken: false
        },
        putUpdate: (idHexString: string) => {
            return {
                method: METHOD.PUT,
                url: `leaves/${idHexString}`,
                dontNeedToken: false
            };
        },
        deleteLeave: (idHexString: string) => {
            return {
                method: METHOD.DELETE,
                url: `leaves/delete/${idHexString}`,
                dontNeedToken: false
            };
        },
        downloadLeave: (idHexString: string) => {
            return {
                method: METHOD.GET,
                url: `leaves/download/${idHexString}`,
                dontNeedToken: false
            };
        }
    },
    manage_ot: {
        getAll: {
            method: METHOD.GET,
            url: 'overTimeTicket/search',
            dontNeedToken: false
        },
        putReject: {
            method: METHOD.PUT,
            url: `overTimeTicket/reject`,
            dontNeedToken: false
        },
        deleteOT: (idHexString: string) => {
            return {
                method: METHOD.DELETE,
                url: `overTimeTicket/delete/${idHexString}`,
                dontNeedToken: false
            };
        },
        downloadOT: (idHexString: string) => {
            return {
                method: METHOD.GET,
                url: `overTimeTicket/download/${idHexString}`,
                dontNeedToken: false
            };
        }
    },
    manage_resignation: {
        getAll: {
            method: METHOD.GET,
            url: 'resignation',
            dontNeedToken: false
        },
        putApproved: (idHexString: string) => {
            return {
                method: METHOD.PUT,
                url: `resignation/approved/${idHexString}`,
                dontNeedToken: false
            };
        },
        postCreate: {
            method: METHOD.POST,
            url: 'resignation',
            dontNeedToken: false
        },
        putUpdate: (idHexString: string) => {
            return {
                method: METHOD.PUT,
                url: `resignation/${idHexString}`,
                dontNeedToken: false
            };
        }
    },
    sale_pipeline: {
        getListYear: {
            method: METHOD.GET,
            url: 'sales/sale-pipeline/listYear',
            dontNeedToken: false
        }
    },
    sale_pipeline_on_going: {
        getAll: {
            method: METHOD.GET,
            url: 'sales/onGoing',
            dontNeedToken: false
        },
        saveOrUpdate: {
            method: METHOD.POST,
            url: 'sales/onGoing/saveOrUpdate',
            dontNeedToken: false
        },
        comment: {
            method: METHOD.POST,
            url: 'sales/onGoing/updateComment',
            dontNeedToken: false
        },
        getTotal: {
            method: METHOD.GET,
            url: 'sales/onGoing/total-ongoing',
            dontNeedToken: false
        }
    },
    sale_pipe_line_bidding: {
        getBidding: {
            method: METHOD.GET,
            url: 'sales/sale-pipeline/bidding',
            dontNeedToken: false
        },
        getMonthlyBillable: {
            method: METHOD.GET,
            url: 'sales/sale-pipeline/monthlyBillable',
            dontNeedToken: false
        },
        estimateHCInfo: {
            method: METHOD.POST,
            url: 'sales/sale-pipeline/estimateHCInfo',
            dontNeedToken: false
        },
        postAddOrEditBidding: {
            method: METHOD.POST,
            url: 'sales/sale-pipeline/bidding/createOrUpdate',
            dontNeedToken: false
        },
        getDetailBidding: {
            method: METHOD.GET,
            url: 'sales/sale-pipeline/bidding/details',
            dontNeedToken: false
        },
        deleteBidding: {
            method: METHOD.DELETE,
            url: 'sales/sale-pipeline/bidding',
            dontNeedToken: false
        },
        comment: {
            method: METHOD.POST,
            url: 'sales/sale-pipeline/bidding/updateComment',
            dontNeedToken: false
        },
        getTotal: {
            method: METHOD.GET,
            url: 'sales/sale-pipeline/total-bidding',
            dontNeedToken: false
        }
    },
    budgeting_plan: {
        getAll: {
            method: METHOD.GET,
            url: 'sales/budgeting-plan',
            dontNeedToken: false
        },
        editBudgetingPlan: {
            method: METHOD.POST,
            url: 'sales/budgeting-plan/saveOrUpdate',
            dontNeedToken: false
        },
        getTotal: {
            method: METHOD.GET,
            url: 'sales/budgeting-plan/total-budgeting-plan',
            dontNeedToken: false
        }
    },
    skills_manage: {
        getReport: {
            method: METHOD.GET,
            url: 'skill/report',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'skill/report/download',
            dontNeedToken: false
        },
        getTitleCodes: {
            method: METHOD.GET,
            url: 'master/titleAll',
            dontNeedToken: false
        },
        getTechAll: {
            method: METHOD.GET,
            url: 'master/techAll'
        },
        getSkillsUpdate: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate',
            dontNeedToken: false
        },
        getDetailCv: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate/getDetail',
            dontNeedToken: false
        },
        getReferences: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate/references',
            dontNeedToken: false
        },
        getTechnologies: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate/technologyAll',
            dontNeedToken: false
        },
        getSkillByTechnology: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate/skillByTech',
            dontNeedToken: false
        },
        createSkillsUpdate: {
            method: METHOD.POST,
            url: 'skill/skillsUpdate/create',
            dontNeedToken: false
        },
        updateCV: {
            method: METHOD.PATCH,
            url: 'skill/skillsUpdate/update',
            dontNeedToken: false
        },
        viewPDF: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate/viewPdf',
            dontNeedToken: false
        },
        downloadCV: {
            method: METHOD.GET,
            url: 'skill/skillsUpdate/download',
            dontNeedToken: false
        }
    },
    product: {
        getProductReportOptions: {
            method: METHOD.GET,
            url: 'project/projectReport/getAllProject',
            dontNeedToken: false
        },
        getReport: {
            method: METHOD.GET,
            url: 'project/projectReport',
            dontNeedToken: false
        },
        getReportByProjectId: (projectId: number) => ({
            method: METHOD.GET,
            url: `project/projectReport/${projectId}`,
            dontNeedToken: false
        })
    },
    leave_day: {
        getAll: {
            method: METHOD.GET,
            url: '/manageLeaveDays',
            dontNeedToken: false
        },
        getLeaveDaysInfo: (idHexString: string) => ({
            method: METHOD.GET,
            url: `/manageLeaveDays/${idHexString}`,
            dontNeedToken: false
        }),
        postSaveOrUpdate: {
            method: METHOD.PUT,
            url: '/manageLeaveDays',
            dontNeedToken: false
        }
    },
    monitor_bidding: {
        getBiddingTracking: {
            method: METHOD.GET,
            url: '/biddingTracking/getBiddingTracking',
            dontNeedToken: false
        },
        getDownload: {
            method: METHOD.GET,
            url: 'monitorBidding/download',
            dontNeedToken: false,
            responseType: 'blob'
        },
        getBiddingReport: {
            method: METHOD.GET,
            url: '/monitorBidding/getBiddingReport',
            dontNeedToken: false
        },
        createOrUpdateBiddingReportpost: {
            method: METHOD.POST,
            url: '/monitorBidding/createBiddingReport',
            dontNeedToken: false
        },
        updateBiddingReportpost: (id: string) => ({
            method: METHOD.PUT,
            url: `/monitorBidding/updateBiddingReport/${id}`,
            dontNeedToken: false
        }),
        postSynchronize: (token: string) => ({
            method: METHOD.POST,
            url: `/biddingTracking/postBiddingTracking?token=${token}`,
            dontNeedToken: false
        })
    },
    flexible_report: {
        get: {
            method: METHOD.GET,
            url: '/flexible-report',
            dontNeedToken: false
        },
        getAllReports: {
            method: METHOD.GET,
            url: '/flexible-report/config',
            dontNeedToken: false
        },
        getConditionTypes: {
            method: METHOD.GET,
            url: '/flexible-columns/findAllByReport',
            dontNeedToken: false
        },
        createConfig: {
            method: METHOD.POST,
            url: '/flexible-report/config/create',
            dontNeedToken: false
        },
        editConfig: (id: string) => ({
            method: METHOD.PUT,
            url: `/flexible-report/config/update/${id}`,
            dontNeedToken: false
        }),
        deleteConfig: (id: string) => ({
            method: METHOD.DELETE,
            url: `/flexible-report/config/${id}`,
            dontNeedToken: false
        }),
        getConditionOptions: {
            method: METHOD.GET,
            url: '/type/all',
            dontNeedToken: false
        },
        editArrangement: {
            method: METHOD.PUT,
            url: 'flexible-report/update-index',
            dontNeedToken: false
        }
    },
    flexible_columns: {
        get: {
            method: METHOD.GET,
            url: '/flexible-columns/columns',
            dontNeedToken: false
        },
        edit: {
            method: METHOD.PUT,
            url: '/flexible-columns',
            dontNeedToken: false
        }
    },
    flexible_textConfig: {
        get: {
            method: METHOD.GET,
            url: '/flexible-text-config/get-all',
            dontNeedToken: false
        },
        edit: {
            method: METHOD.PUT,
            url: '/flexible-text-config/update',
            dontNeedToken: false
        },
        configByLanguage: {
            method: METHOD.GET,
            url: '/flexible-text-config/config-by-language',
            dontNeedToken: true
        },
        getLanguage: {
            method: METHOD.GET,
            url: '/flexible-language',
            dontNeedToken: false
        },
        addLanguage: {
            method: METHOD.POST,
            url: '/flexible-language/create',
            dontNeedToken: false
        },
        deleteLanguage: (id: string) => ({
            method: METHOD.DELETE,
            url: `/flexible-language/${id}`,
            dontNeedToken: false
        })
    },
    overtime_report: {
        postOvertimeTicket: {
            method: METHOD.POST,
            url: 'overTimeTicket',
            dontNeedToken: false
        },
        getDetailOvertimeTicket: (idHexString: string) => ({
            method: METHOD.GET,
            url: `overTimeTicket/detail/${idHexString}`,
            dontNeedToken: false
        }),
        updateDetailOvertimeTicket: (idHexString: string) => ({
            method: METHOD.PUT,
            url: `overTimeTicket/update/${idHexString}`,
            dontNeedToken: false
        }),
        approvedOvertimeTicket: (idHexString: string) => ({
            method: METHOD.PUT,
            url: `overTimeTicket/approved/${idHexString}`,
            dontNeedToken: false
        })
    },
    payment_status: {
        getAllPaymentStatus: {
            method: METHOD.GET,
            url: 'paymentStatus',
            dontNeedToken: false
        }
    }
};

export default Api;

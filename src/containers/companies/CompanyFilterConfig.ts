import { ICompanyFilterOption } from 'types/company';

// Company industry filter options
export const companyIndustryOptions: ICompanyFilterOption[] = [
    { value: 'all', label: 'Tất cả lĩnh vực' },
    { value: 'technology', label: 'Công nghệ thông tin' },
    { value: 'finance', label: 'Tà<PERSON> chính - Ngân hàng' },
    { value: 'healthcare', label: 'Y tế - Sức khỏe' },
    { value: 'education', label: 'Giáo dục - Đào tạo' },
    { value: 'manufacturing', label: 'Sản xuất - Chế tạo' },
    { value: 'retail', label: 'Bán lẻ - Thương mại' },
    { value: 'consulting', label: 'Tư vấn' },
    { value: 'media', label: 'Truyền thông - Quảng cáo' },
    { value: 'real-estate', label: 'Bất động sản' },
    { value: 'logistics', label: 'Vận tải - Logistics' }
];

// Default company filter state
export const defaultCompanyFilter = 'all';

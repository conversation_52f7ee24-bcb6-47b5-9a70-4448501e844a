import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// yup
// @ts-ignore - TypeScript module resolution issue with @hookform/resolvers
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, Grid, IconButton, InputAdornment, Stack, Divider, Typography, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { authSelector, loginRequest } from 'store/slice/authSlice';
import { useAppSelector, useAppDispatch } from 'app/hooks';
import { FormProvider, Input, Checkbox } from 'components/extended/Form';
import { ILoginConfig, loginConfig, loginSchema } from 'pages/Config';
import { ROUTER } from 'constants/Routers';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import GoogleIcon from '@mui/icons-material/Google';

// ============================|| LOGIN ||============================ //

const AuthLogin = () => {
    const [showPassword, setShowPassword] = useState(false);

    const { loading } = useAppSelector(authSelector);
    const theme = useTheme();
    const navigate = useNavigate();
    const dispatch = useAppDispatch();

    const handleSubmit = async (values: ILoginConfig) => {
        try {
            const result = await dispatch(
                loginRequest({
                    username: values.username,
                    password: values.password,
                    ldap: false
                })
            ).unwrap();

            if (result?.status) {
                // Login successful, navigate to dashboard
                navigate('/dashboard');
            }
        } catch (error) {
            console.error('Login failed:', error);
        }
    };

    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    const handleGoogleLogin = () => {
        console.log('Login with Google');
        // TODO: Implement Google login
    };

    return (
        <FormProvider
            form={{
                defaultValues: loginConfig,
                resolver: yupResolver(loginSchema)
            }}
            onSubmit={handleSubmit}
        >
            <Box sx={{ width: '100%' }}>
                <Stack spacing={3}>
                    {/* Google Login Button */}
                    <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<GoogleIcon />}
                        onClick={handleGoogleLogin}
                        sx={{
                            py: 1.5,
                            borderColor: theme.palette.grey[300],
                            color: theme.palette.text.primary,
                            textTransform: 'none',
                            fontWeight: 700,
                            fontSize: 14,
                            borderRadius: 1,
                            '&:hover': {
                                borderColor: theme.palette.grey[400],
                                backgroundColor: theme.palette.grey[50]
                            }
                        }}
                    >
                        Continue with Google
                    </Button>

                    {/* OR Divider */}
                    <Box sx={{ textAlign: 'center', position: 'relative' }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: 12, fontWeight: 600 }}>
                            ------------- or Sign in with Email -------------
                        </Typography>
                    </Box>

                    {/* Email Field */}
                    <Stack spacing={1}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: 14,
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 0.5
                            }}
                        >
                            Email
                        </Typography>
                        <Input
                            name="username"
                            placeholder="<EMAIL>"
                            textFieldProps={{
                                size: 'medium',
                                autoComplete: 'username',
                                fullWidth: true,
                                sx: {
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 1,
                                        fontSize: 14,
                                        '& .MuiInputBase-input::placeholder': {
                                            color: theme.palette.grey[400],
                                            opacity: 1
                                        }
                                    }
                                }
                            }}
                        />
                    </Stack>

                    {/* Password Field */}
                    <Stack spacing={1}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: 14,
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 0.5
                            }}
                        >
                            Password
                        </Typography>
                        <Input
                            name="password"
                            placeholder="*****************"
                            type={showPassword ? 'text' : 'password'}
                            textFieldProps={{
                                size: 'medium',
                                autoComplete: 'current-password',
                                fullWidth: true,
                                InputProps: {
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={handleClickShowPassword}
                                                edge="end"
                                                size="medium"
                                            >
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                },
                                sx: {
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 1,
                                        fontSize: 14,
                                        '& .MuiInputBase-input::placeholder': {
                                            color: theme.palette.grey[400],
                                            opacity: 1
                                        }
                                    }
                                }
                            }}
                        />
                    </Stack>

                    {/* Remember Me & Forgot Password */}
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Checkbox
                            name="rememberMe"
                            label="Remember Me"
                            checkboxProps={{
                                size: 'small',
                                sx: {
                                    '& .MuiSvgIcon-root': {
                                        fontSize: 16
                                    }
                                }
                            }}
                        />
                        <Button
                            variant="text"
                            size="small"
                            onClick={() => navigate('/forgot-password')}
                            sx={{
                                textTransform: 'none',
                                color: theme.palette.primary.main,
                                fontWeight: 600,
                                fontSize: 12,
                                p: 0,
                                minWidth: 'auto',
                                '&:hover': {
                                    backgroundColor: 'transparent',
                                    textDecoration: 'underline'
                                }
                            }}
                        >
                            Forgot Password?
                        </Button>
                    </Stack>

                    {/* Login Button */}
                    <LoadingButton
                        fullWidth
                        loading={loading?.login}
                        variant="contained"
                        type="submit"
                        size="large"
                        sx={{
                            py: 1.5,
                            borderRadius: 1.5,
                            textTransform: 'none',
                            fontSize: 18,
                            fontWeight: 800,
                            backgroundColor: theme.palette.primary.main,
                            color: 'white',
                            '&:hover': {
                                backgroundColor: theme.palette.primary.dark
                            }
                        }}
                    >
                        Login
                    </LoadingButton>
                </Stack>
            </Box>
        </FormProvider>
    );
};

export default AuthLogin;

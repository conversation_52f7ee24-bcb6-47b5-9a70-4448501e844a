import { Box } from '@mui/material';

// project imports
import {
    HeroSearch,
    CategoriesSection,
    FeaturedJobsSection,
    HowItWorksSection,
    BlogSection,
    CTASection,
    CompanyLogosSection
} from '../../containers/homepage';
import { CompaniesSection } from '../../containers/companies';

// ================================|| HOMEPAGE ||================================ //

const Homepage = () => {
    // TODO: Get authentication state from context/store
    const isLoggedIn = false; // This should come from auth context

    return (
        <Box>
            {/* Hero Section with Search */}
            <HeroSearch />

            {/* Popular Job Categories */}
            <CategoriesSection />

            {/* Featured Jobs */}
            <FeaturedJobsSection />

            {/* Company Logos */}
            <CompanyLogosSection />

            {/* How It Works */}
            <HowItWorksSection />

            {/* Companies Section */}
            <CompaniesSection />

            {/* Recent News */}
            <BlogSection />

            {/* CTA Section - Only show when not logged in */}
            <CTASection isLoggedIn={isLoggedIn} />
        </Box>
    );
};

export default Homepage;

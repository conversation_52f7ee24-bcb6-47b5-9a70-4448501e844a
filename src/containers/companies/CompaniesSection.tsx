import React, { useState, useMemo } from 'react';
import { Box, Container, Typography, useTheme } from '@mui/material';

// project imports
import CompanyFilterSection from './CompanyFilterSection';
import CompanyList from './CompanyList';
import { ICompanyWithInteraction } from 'types/company';

// Mock company data - replace with real data from API
const mockCompanies: ICompanyWithInteraction[] = [
    {
        id: 1,
        name: 'FPT Software',
        industry: 'Công nghệ thông tin',
        location: 'Hà Nội',
        size: 'Lớn (1000+ người)',
        openPositions: 25,
        logo: 'https://via.placeholder.com/64x64/1976D2/FFFFFF?text=FPT',
        isVerified: true,
        isFollowed: false,
        description: 'Công ty phần mềm hàng đầu Việt Nam'
    },
    {
        id: 2,
        name: 'Vietcombank',
        industry: '<PERSON><PERSON><PERSON> ch<PERSON>h - <PERSON><PERSON> hàng',
        location: '<PERSON><PERSON>',
        size: 'Tập đoàn (5000+ người)',
        openPositions: 18,
        logo: 'https://via.placeholder.com/64x64/4CAF50/FFFFFF?text=VCB',
        isVerified: true,
        isFollowed: true,
        description: 'Ngân hàng thương mại cổ phần Ngoại thương Việt Nam'
    },
    {
        id: 3,
        name: 'Vingroup',
        industry: 'Bất động sản',
        location: 'Hà Nội',
        size: 'Tập đoàn (5000+ người)',
        openPositions: 42,
        logo: 'https://via.placeholder.com/64x64/E91E63/FFFFFF?text=VIN',
        isVerified: true,
        isFollowed: false,
        description: 'Tập đoàn kinh tế tư nhân đa ngành lớn nhất Việt Nam'
    },
    {
        id: 4,
        name: 'Shopee Vietnam',
        industry: 'Bán lẻ - Thương mại',
        location: 'Hồ Chí Minh',
        size: 'Lớn (1000+ người)',
        openPositions: 33,
        logo: 'https://via.placeholder.com/64x64/FF5722/FFFFFF?text=SPE',
        isVerified: true,
        isFollowed: false,
        description: 'Nền tảng thương mại điện tử hàng đầu Đông Nam Á'
    },
    {
        id: 5,
        name: 'Grab Vietnam',
        industry: 'Công nghệ thông tin',
        location: 'Hồ Chí Minh',
        size: 'Lớn (1000+ người)',
        openPositions: 15,
        logo: 'https://via.placeholder.com/64x64/00C853/FFFFFF?text=GRB',
        isVerified: true,
        isFollowed: true,
        description: 'Ứng dụng siêu đa dịch vụ hàng đầu Đông Nam Á'
    },
    {
        id: 6,
        name: 'Techcombank',
        industry: 'Tài chính - Ngân hàng',
        location: 'Hà Nội',
        size: 'Lớn (1000+ người)',
        openPositions: 12,
        logo: 'https://via.placeholder.com/64x64/2196F3/FFFFFF?text=TCB',
        isVerified: true,
        isFollowed: false,
        description: 'Ngân hàng Thương mại Cổ phần Kỹ thương Việt Nam'
    }
];

interface CompaniesSectionProps {
    title?: string;
    showTitle?: boolean;
    maxItems?: number;
}

const CompaniesSection: React.FC<CompaniesSectionProps> = ({ title = 'Công ty hàng đầu', showTitle = true, maxItems }) => {
    const theme = useTheme();
    const [currentFilter, setCurrentFilter] = useState<string>('all');
    const [loading, setLoading] = useState(false);

    // Filter companies based on current industry filter
    const filteredCompanies = useMemo(() => {
        let filtered = [...mockCompanies];

        if (currentFilter !== 'all') {
            const industryMap: { [key: string]: string } = {
                technology: 'Công nghệ thông tin',
                finance: 'Tài chính - Ngân hàng',
                'real-estate': 'Bất động sản',
                retail: 'Bán lẻ - Thương mại'
            };

            filtered = filtered.filter((company) => {
                return company.industry === industryMap[currentFilter];
            });
        }

        return maxItems ? filtered.slice(0, maxItems) : filtered;
    }, [currentFilter, maxItems]);

    const handleFilterChange = (industryFilter: string) => {
        setCurrentFilter(industryFilter);
        // In real app, this would trigger API call
        setLoading(true);
        setTimeout(() => setLoading(false), 500);
    };

    const handleCompanyClick = (company: ICompanyWithInteraction) => {
        console.log('Company clicked:', company);
        // Navigate to company detail page
    };

    const handleFollowClick = (company: ICompanyWithInteraction) => {
        console.log('Follow clicked:', company);
        // Handle follow/unfollow logic
    };

    return (
        <Box sx={{ py: { xs: 6, md: 8 }, backgroundColor: 'background.default' }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                {showTitle && (
                    <Box textAlign="center" mb={6}>
                        <Typography
                            variant="h2"
                            component="h2"
                            gutterBottom
                            sx={{
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 2,
                                fontSize: { xs: '2.25rem', md: '3rem' }
                            }}
                        >
                            {title}
                        </Typography>
                        <Typography
                            variant="h6"
                            color="text.secondary"
                            sx={{
                                fontWeight: 400,
                                maxWidth: 600,
                                mx: 'auto'
                            }}
                        >
                            Khám phá các công ty hàng đầu đang tuyển dụng
                        </Typography>
                    </Box>
                )}

                {/* Full-width Filter Section */}
                {/* <Box
                        sx={{
                            width: '100%',
                            mb: 4,
                            backgroundColor: 'background.paper',
                            borderRadius: 2,
                            border: '1px solid',
                            borderColor: 'divider',
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                        }}
                    >
                    
                    </Box> */}
                {/* <CompanyFilterSection onFilterChange={handleFilterChange} /> */}
                {/* Company List */}
                <CompanyList
                    companies={filteredCompanies}
                    loading={loading}
                    variant="grid"
                    itemVariant="default"
                    showLoadMore={!maxItems && filteredCompanies.length >= 6}
                    onCompanyClick={handleCompanyClick}
                    onFollowClick={handleFollowClick}
                    onLoadMore={() => console.log('Load more companies')}
                />
            </Container>
        </Box>
    );
};

export default CompaniesSection;

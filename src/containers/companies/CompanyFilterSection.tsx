import React, { useState } from 'react';
import { Box, FormControl, Select, MenuItem, InputLabel, SelectChangeEvent, Tabs, Tab, useTheme, useMediaQuery } from '@mui/material';

// project imports
import { companyIndustryOptions, defaultCompanyFilter } from './CompanyFilterConfig';

interface ICompanyFilterSectionProps {
    onFilterChange?: (industryFilter: string) => void;
}

const CompanyFilterSection: React.FC<ICompanyFilterSectionProps> = ({ onFilterChange }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [selectedIndustry, setSelectedIndustry] = useState<string>(defaultCompanyFilter);

    // Handle filter value change (dropdown)
    const handleFilterValueChange = (event: SelectChangeEvent<string>) => {
        const newValue = event.target.value;
        setSelectedIndustry(newValue);
        onFilterChange?.(newValue);
    };

    // Handle tab change (desktop)
    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        setSelectedIndustry(newValue);
        onFilterChange?.(newValue);
    };

    return (
        <Box
            sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start',
                mb: { xs: 3, md: 4 },
                px: { xs: 1, sm: 2, md: 2 },
                py: { xs: 0, md: 2 },
                width: '100%'
            }}
        >
            {isMobile ? (
                /* Mobile: Dropdown */
                <FormControl
                    size="medium"
                    sx={{
                        minWidth: '100%'
                    }}
                >
                    <InputLabel sx={{ fontWeight: 500 }}>Lọc theo lĩnh vực</InputLabel>
                    <Select
                        value={selectedIndustry}
                        onChange={handleFilterValueChange}
                        label="Lọc theo lĩnh vực"
                        sx={{
                            '& .MuiSelect-select': {
                                fontWeight: 500
                            }
                        }}
                    >
                        {companyIndustryOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value} sx={{ py: 1.5 }}>
                                {option.label}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            ) : (
                /* Desktop: Custom Pill-style Tabs */
                <Box
                    sx={{
                        flexGrow: 1,
                        display: 'flex',
                        alignItems: 'center',
                        overflow: 'hidden',
                        position: 'relative'
                    }}
                >
                    <Tabs
                        value={selectedIndustry}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        allowScrollButtonsMobile
                        sx={{
                            minHeight: 'auto',
                            '& .MuiTabs-flexContainer': {
                                gap: 1
                            },
                            '& .MuiTab-root': {
                                minHeight: 40,
                                minWidth: 'auto',
                                px: 2.5,
                                py: 1,
                                borderRadius: 20,
                                textTransform: 'none',
                                fontWeight: 500,
                                fontSize: '0.875rem',
                                color: theme.palette.text.secondary,
                                backgroundColor: 'transparent',
                                border: '1px solid',
                                borderColor: theme.palette.divider,
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    backgroundColor: theme.palette.action.hover,
                                    borderColor: theme.palette.primary.light
                                },
                                '&.Mui-selected': {
                                    color: theme.palette.primary.contrastText,
                                    backgroundColor: theme.palette.primary.main,
                                    borderColor: theme.palette.primary.main,
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.dark,
                                        borderColor: theme.palette.primary.dark
                                    }
                                }
                            },
                            '& .MuiTabs-indicator': {
                                display: 'none'
                            },
                            '& .MuiTabs-scrollButtons': {
                                color: theme.palette.text.secondary,
                                '&.Mui-disabled': {
                                    opacity: 0.3
                                }
                            }
                        }}
                    >
                        {companyIndustryOptions.map((option) => (
                            <Tab key={option.value} label={option.label} value={option.value} />
                        ))}
                    </Tabs>
                </Box>
            )}
        </Box>
    );
};

export default CompanyFilterSection;

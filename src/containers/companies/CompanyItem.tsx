import React from 'react';
import { Box, Typography, IconButton, Avatar, useTheme, Chip } from '@mui/material';
import { BusinessOutlined as CompanyIcon, WorkOutlineOutlined as JobIcon, Add as PlusIcon, Check as CheckIcon } from '@mui/icons-material';

// project imports
import MainCard from 'components/cards/MainCard';
import { ICompanyItemProps } from 'types/company';

const CompanyItem: React.FC<ICompanyItemProps> = ({
    company,
    variant = 'default',
    showFollow = true,
    showDetails = true,
    onCompanyClick,
    onFollowClick,
    sx = {}
}) => {
    const theme = useTheme();

    const handleCompanyClick = () => {
        if (onCompanyClick) {
            onCompanyClick(company);
        }
    };

    const handleFollowClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        if (onFollowClick) {
            onFollowClick(company, event);
        }
    };

    const getCardSx = () => {
        return {
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            borderRadius: 2,
            position: 'relative',
            border: '1px solid',
            borderColor: 'grey.200',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            backgroundColor: 'background.paper',
            '&:hover': {
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
                borderColor: 'grey.300',
                '& .hover-follow-button': {
                    opacity: 1,
                    visibility: 'visible'
                }
            }
        };
    };

    const companyContent = (
        <Box position="relative" p={2}>
            {/* Top Section - Logo + Company Info */}
            <Box display="flex" gap={2} mb={2}>
                {/* Company Logo - Large Square */}
                <Avatar
                    src={company.logo}
                    variant="rounded"
                    sx={{
                        width: 64,
                        height: 64,
                        backgroundColor: theme.palette.grey[100],
                        color: theme.palette.grey[600],
                        flexShrink: 0,
                        borderRadius: 1.5
                    }}
                >
                    <CompanyIcon fontSize="large" />
                </Avatar>

                {/* Company Info */}
                <Box flex={1} minWidth={0}>
                    {/* Company Name */}
                    <Typography
                        variant="h6"
                        component="h3"
                        sx={{
                            fontWeight: 600,
                            fontSize: '1.1rem',
                            color: theme.palette.text.primary,
                            mb: 0.5,
                            lineHeight: 1.3,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                        }}
                    >
                        {company.name}
                    </Typography>

                    {/* Industry */}
                    <Typography
                        variant="body2"
                        sx={{
                            color: theme.palette.text.secondary,
                            fontSize: '0.875rem',
                            mb: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                        }}
                    >
                        {company.industry}
                    </Typography>
                </Box>
            </Box>

            {/* Bottom Section - Open Positions (aligned below logo) */}
            <Box display="flex" alignItems="center" gap={1} flexWrap="wrap" pl={0}>
                {/* Open Positions Chip */}
                <Chip
                    icon={<JobIcon sx={{ fontSize: '0.875rem !important' }} />}
                    label={`${company.openPositions} vị trí đang tuyển`}
                    size="small"
                    sx={{
                        backgroundColor: theme.palette.primary.light,
                        color: theme.palette.primary.main,
                        fontWeight: 500,
                        fontSize: '0.75rem',
                        height: 28,
                        '& .MuiChip-label': {
                            px: 1.5,
                            color: theme.palette.primary.main
                        },
                        '& .MuiChip-icon': {
                            color: theme.palette.primary.main
                        }
                    }}
                />
            </Box>

            {/* Hover Follow Button */}
            {showFollow && (
                <Box
                    className="hover-follow-button"
                    sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        opacity: 0,
                        visibility: 'hidden',
                        transition: 'all 0.2s ease',
                        zIndex: 2
                    }}
                >
                    <Box
                        onClick={handleFollowClick}
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                            px: 1.5,
                            py: 0.75,
                            backgroundColor: company.isFollowed ? theme.palette.success.main : theme.palette.primary.main,
                            color: 'white',
                            borderRadius: 1,
                            cursor: 'pointer',
                            fontSize: '0.75rem',
                            fontWeight: 500,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: company.isFollowed ? theme.palette.success.dark : theme.palette.primary.dark
                            }
                        }}
                    >
                        {company.isFollowed ? (
                            <>
                                <CheckIcon sx={{ fontSize: '0.875rem' }} />
                                <Typography variant="caption" sx={{ fontSize: '0.75rem', fontWeight: 500, color: 'white' }}>
                                    Đang theo dõi
                                </Typography>
                            </>
                        ) : (
                            <>
                                <PlusIcon sx={{ fontSize: '0.875rem' }} />
                                <Typography variant="caption" sx={{ fontSize: '0.75rem', fontWeight: 500, color: 'white' }}>
                                    Theo dõi
                                </Typography>
                            </>
                        )}
                    </Box>
                </Box>
            )}
        </Box>
    );

    return (
        <MainCard
            content={false}
            sx={{
                ...getCardSx(),
                ...sx
            }}
            onClick={handleCompanyClick}
        >
            {companyContent}
        </MainCard>
    );
};

export default CompanyItem;
